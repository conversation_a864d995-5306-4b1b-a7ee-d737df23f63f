<!-- Classes & Streams Management Tab -->
<div class="space-y-6">
    <!-- Add Class Form -->
    <div class="bg-gray-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Add New Class</h3>
        <form action="<?php echo e(route('admin.classes.store')); ?>" method="POST" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <?php echo csrf_field(); ?>
            <div>
                <label class="block text-sm font-medium text-gray-700">Class Name</label>
                <input type="text" name="name" placeholder="e.g., Class 1, Class 2" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mt-6">
                    Add Class
                </button>
            </div>
        </form>
    </div>

    <!-- Add Stream Form -->
    <div class="bg-gray-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Add New Stream</h3>
        <form action="<?php echo e(route('admin.streams.store')); ?>" method="POST" class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <?php echo csrf_field(); ?>
            <div>
                <label class="block text-sm font-medium text-gray-700">Select Class</label>
                <select name="class_id" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    <option value="">Choose Class</option>
                    <?php $__currentLoopData = \App\Models\SchoolClass::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $class): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($class->id); ?>"><?php echo e($class->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Stream Name</label>
                <input type="text" name="name" placeholder="e.g., A, B, C" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <button type="submit" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded mt-6">
                    Add Stream
                </button>
            </div>
        </form>
    </div>

    <!-- Classes and Streams List -->
    <div>
        <h3 class="text-lg font-semibold mb-4">Classes and Streams</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Classes -->
            <div>
                <h4 class="font-medium mb-2">Classes</h4>
                <div class="border rounded">
                    <?php $__currentLoopData = \App\Models\SchoolClass::withCount(['streams', 'enrollments'])->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $class): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="p-3 border-b last:border-b-0 flex justify-between items-center">
                            <div>
                                <span class="font-medium"><?php echo e($class->name); ?></span>
                                <span class="text-sm text-gray-500 ml-2">
                                    <?php echo e($class->streams_count); ?> streams, <?php echo e($class->enrollments_count); ?> students
                                </span>
                            </div>
                            <div>
                                <a href="<?php echo e(route('admin.classes.edit', $class)); ?>" class="text-blue-600 hover:text-blue-900 mr-2">Edit</a>
                                <form action="<?php echo e(route('admin.classes.destroy', $class)); ?>" method="POST" class="inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="text-red-600 hover:text-red-900" 
                                            onclick="return confirm('Are you sure?')">Delete</button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>

            <!-- Streams -->
            <div>
                <h4 class="font-medium mb-2">Streams</h4>
                <div class="border rounded">
                    <?php $__currentLoopData = \App\Models\Stream::with(['schoolClass'])->withCount(['enrollments'])->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stream): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="p-3 border-b last:border-b-0 flex justify-between items-center">
                            <div>
                                <span class="font-medium"><?php echo e($stream->schoolClass->name); ?> <?php echo e($stream->name); ?></span>
                                <span class="text-sm text-gray-500 ml-2">
                                    <?php echo e($stream->enrollments_count); ?> students
                                </span>
                            </div>
                            <div>
                                <a href="<?php echo e(route('admin.streams.edit', $stream)); ?>" class="text-blue-600 hover:text-blue-900 mr-2">Edit</a>
                                <form action="<?php echo e(route('admin.streams.destroy', $stream)); ?>" method="POST" class="inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="text-red-600 hover:text-red-900" 
                                            onclick="return confirm('Are you sure?')">Delete</button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\school_results\resources\views/admin/tabs/classes.blade.php ENDPATH**/ ?>