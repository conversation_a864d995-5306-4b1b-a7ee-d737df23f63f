<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('School Management System - Admin Panel')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-6">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Statistics Cards -->
            <div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
                <div class="bg-white p-4 rounded shadow text-center">
                    <div class="text-2xl font-bold text-blue-600"><?php echo e($stats['total_students']); ?></div>
                    <div class="text-sm text-gray-600">Students</div>
                </div>
                <div class="bg-white p-4 rounded shadow text-center">
                    <div class="text-2xl font-bold text-green-600"><?php echo e($stats['total_teachers']); ?></div>
                    <div class="text-sm text-gray-600">Teachers</div>
                </div>
                <div class="bg-white p-4 rounded shadow text-center">
                    <div class="text-2xl font-bold text-purple-600"><?php echo e($stats['total_classes']); ?></div>
                    <div class="text-sm text-gray-600">Classes</div>
                </div>
                <div class="bg-white p-4 rounded shadow text-center">
                    <div class="text-2xl font-bold text-orange-600"><?php echo e($stats['total_subjects']); ?></div>
                    <div class="text-sm text-gray-600">Subjects</div>
                </div>
                <div class="bg-white p-4 rounded shadow text-center">
                    <div class="text-2xl font-bold text-red-600"><?php echo e($stats['current_enrollments']); ?></div>
                    <div class="text-sm text-gray-600">Enrollments</div>
                </div>
            </div>

            <!-- Tabbed Interface -->
            <div class="bg-white shadow rounded-lg">
                <!-- Tab Navigation -->
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                        <button onclick="showTab('users')" id="users-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Users
                        </button>
                        <button onclick="showTab('classes')" id="classes-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Classes & Streams
                        </button>
                        <button onclick="showTab('subjects')" id="subjects-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Subjects
                        </button>
                        <button onclick="showTab('enrollments')" id="enrollments-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Student Enrollments
                        </button>
                        <button onclick="showTab('assignments')" id="assignments-tab" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Teacher Assignments
                        </button>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="p-6">
                    <!-- Users Tab -->
                    <div id="users-content" class="tab-content hidden">
                        <?php echo $__env->make('admin.tabs.users', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>

                    <!-- Classes Tab -->
                    <div id="classes-content" class="tab-content hidden">
                        <?php echo $__env->make('admin.tabs.classes', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>

                    <!-- Subjects Tab -->
                    <div id="subjects-content" class="tab-content hidden">
                        <?php echo $__env->make('admin.tabs.subjects', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>

                    <!-- Enrollments Tab -->
                    <div id="enrollments-content" class="tab-content hidden">
                        <?php echo $__env->make('admin.tabs.enrollments', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>

                    <!-- Assignments Tab -->
                    <div id="assignments-content" class="tab-content hidden">
                        <?php echo $__env->make('admin.tabs.assignments', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('border-blue-500', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected tab content
            document.getElementById(tabName + '-content').classList.remove('hidden');

            // Add active class to selected tab
            const activeTab = document.getElementById(tabName + '-tab');
            activeTab.classList.remove('border-transparent', 'text-gray-500');
            activeTab.classList.add('border-blue-500', 'text-blue-600');
        }

        // Show first tab by default
        document.addEventListener('DOMContentLoaded', function() {
            showTab('users');
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>


<?php /**PATH C:\xampp\htdocs\school_results\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>