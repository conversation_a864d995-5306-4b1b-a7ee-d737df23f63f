<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            <?php echo e(__('Admin Dashboard')); ?>

        </h2>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-blue-600"><?php echo e($stats['total_students']); ?></div>
                        <div class="text-sm text-gray-600">Total Students</div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-green-600"><?php echo e($stats['total_teachers']); ?></div>
                        <div class="text-sm text-gray-600">Total Teachers</div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-purple-600"><?php echo e($stats['total_classes']); ?></div>
                        <div class="text-sm text-gray-600">Total Classes</div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-orange-600"><?php echo e($stats['total_subjects']); ?></div>
                        <div class="text-sm text-gray-600">Total Subjects</div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center">
                        <div class="text-3xl font-bold text-red-600"><?php echo e($stats['current_enrollments']); ?></div>
                        <div class="text-sm text-gray-600">Current Enrollments</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-8">
                <div class="p-6">
                    <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
                    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                        <!-- User Management -->
                        <a href="<?php echo e(route('admin.users.index')); ?>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded text-center">
                            <div class="text-sm">👥</div>
                            <div>Manage Users</div>
                        </a>

                        <!-- Class Management -->
                        <a href="<?php echo e(route('admin.classes.index')); ?>" class="bg-green-500 hover:bg-green-700 text-white font-bold py-3 px-4 rounded text-center">
                            <div class="text-sm">🏫</div>
                            <div>Manage Classes</div>
                        </a>

                        <!-- Stream Management -->
                        <a href="<?php echo e(route('admin.streams.index')); ?>" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-3 px-4 rounded text-center">
                            <div class="text-sm">📚</div>
                            <div>Manage Streams</div>
                        </a>

                        <!-- Subject Management -->
                        <a href="<?php echo e(route('admin.subjects.index')); ?>" class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-3 px-4 rounded text-center">
                            <div class="text-sm">📖</div>
                            <div>Manage Subjects</div>
                        </a>

                        <!-- Enrollment Management -->
                        <a href="<?php echo e(route('admin.enrollments.index')); ?>" class="bg-red-500 hover:bg-red-700 text-white font-bold py-3 px-4 rounded text-center">
                            <div class="text-sm">📝</div>
                            <div>Manage Enrollments</div>
                        </a>
                    </div>

                    <!-- Secondary Actions -->
                    <div class="mt-6">
                        <h4 class="text-md font-semibold mb-3 text-gray-700">Quick Add</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                            <a href="<?php echo e(route('admin.users.create')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-medium py-2 px-3 rounded text-center text-sm">
                                + Add User
                            </a>
                            <a href="<?php echo e(route('admin.classes.create')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-medium py-2 px-3 rounded text-center text-sm">
                                + Add Class
                            </a>
                            <a href="<?php echo e(route('admin.streams.create')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-medium py-2 px-3 rounded text-center text-sm">
                                + Add Stream
                            </a>
                            <a href="<?php echo e(route('admin.enrollments.create')); ?>" class="bg-gray-500 hover:bg-gray-700 text-white font-medium py-2 px-3 rounded text-center text-sm">
                                + Enroll Student
                            </a>
                        </div>
                    </div>

                    </div>
                </div>
            </div>

            <!-- Recent Enrollments -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-semibold mb-4">Recent Enrollments</h3>
                    <?php if($recent_enrollments->count() > 0): ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full table-auto">
                                <thead>
                                    <tr class="bg-gray-50">
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stream</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Academic Year</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enrolled</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php $__currentLoopData = $recent_enrollments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $enrollment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                <?php echo e($enrollment->user->name); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <?php echo e($enrollment->schoolClass->name); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <?php echo e($enrollment->stream->name); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <?php echo e($enrollment->academic_year); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <?php echo e($enrollment->created_at->format('M d, Y')); ?>

                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-gray-500">No recent enrollments found.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\xampp\htdocs\school_results\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>