<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Manage Grades') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {{ session('success') }}
                </div>
            @endif

            <!-- Subject and Term Selection -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6">
                    <form method="GET" action="{{ route('teacher.grades.index') }}" class="flex gap-4 items-end">
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-700">Subject</label>
                            <select name="subject" id="subject" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                <option value="">Select Subject</option>
                                @foreach($assignedSubjects as $assignment)
                                    <option value="{{ $assignment->subject->id }}" {{ $selectedSubject == $assignment->subject->id ? 'selected' : '' }}>
                                        {{ $assignment->subject->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div>
                            <label for="term" class="block text-sm font-medium text-gray-700">Term</label>
                            <select name="term" id="term" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="1" {{ $selectedTerm == 1 ? 'selected' : '' }}>Term 1</option>
                                <option value="2" {{ $selectedTerm == 2 ? 'selected' : '' }}>Term 2</option>
                                <option value="3" {{ $selectedTerm == 3 ? 'selected' : '' }}>Term 3</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Load Students
                        </button>
                    </form>
                </div>
            </div>

            @if($selectedSubject && $enrollments->count() > 0)
                <!-- Grade Entry Form -->
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-semibold mb-4">Enter Grades - Term {{ $selectedTerm }}</h3>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full table-auto">
                                <thead>
                                    <tr class="bg-gray-50">
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Class</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assignment (30%)</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Midterm (30%)</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Final (40%)</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($enrollments as $enrollment)
                                        @php
                                            $existingGrade = $grades->get($enrollment->id);
                                        @endphp
                                        <tr>
                                            <form action="{{ route('teacher.grades.store') }}" method="POST" class="contents">
                                                @csrf
                                                <input type="hidden" name="enrollment_id" value="{{ $enrollment->id }}">
                                                <input type="hidden" name="subject_id" value="{{ $selectedSubject }}">
                                                <input type="hidden" name="term" value="{{ $selectedTerm }}">
                                                
                                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                    {{ $enrollment->user->name }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {{ $enrollment->schoolClass->name }} {{ $enrollment->stream->name }}
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <input type="number" name="assignment" min="0" max="100" step="0.1" 
                                                           value="{{ $existingGrade ? $existingGrade->assignment : '' }}"
                                                           class="w-20 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <input type="number" name="midterm" min="0" max="100" step="0.1" 
                                                           value="{{ $existingGrade ? $existingGrade->midterm : '' }}"
                                                           class="w-20 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <input type="number" name="final" min="0" max="100" step="0.1" 
                                                           value="{{ $existingGrade ? $existingGrade->final : '' }}"
                                                           class="w-20 rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" required>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    @if($existingGrade)
                                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                            @if($existingGrade->total >= 80) bg-green-100 text-green-800
                                                            @elseif($existingGrade->total >= 60) bg-yellow-100 text-yellow-800
                                                            @else bg-red-100 text-red-800 @endif">
                                                            {{ number_format($existingGrade->total, 1) }}%
                                                        </span>
                                                    @else
                                                        <span class="text-gray-400">Not calculated</span>
                                                    @endif
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-3 rounded text-sm">
                                                        Save
                                                    </button>
                                                </td>
                                            </form>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @elseif($selectedSubject)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center text-gray-500">
                        No students enrolled for the current academic year.
                    </div>
                </div>
            @else
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6 text-center text-gray-500">
                        Please select a subject to view students and enter grades.
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
