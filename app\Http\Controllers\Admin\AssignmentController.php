<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Subject;
use App\Models\SchoolClass;
use App\Models\Stream;
use App\Models\SubjectTeacher;
use App\Models\ClassTeacher;
use Illuminate\Http\Request;

class AssignmentController extends Controller
{
    /**
     * Assign teacher to subject
     */
    public function assignTeacherToSubject(Request $request)
    {
        $request->validate([
            'teacher_id' => 'required|exists:users,id',
            'subject_id' => 'required|exists:subjects,id',
        ]);

        // Check if teacher exists and is actually a teacher
        $teacher = User::where('id', $request->teacher_id)->where('role', 'teacher')->first();
        if (!$teacher) {
            return back()->withErrors(['teacher_id' => 'Selected user is not a teacher.']);
        }

        // Check if assignment already exists
        $exists = SubjectTeacher::where('user_id', $request->teacher_id)
                               ->where('subject_id', $request->subject_id)
                               ->exists();

        if ($exists) {
            return back()->withErrors(['teacher_id' => 'This teacher is already assigned to this subject.']);
        }

        SubjectTeacher::create([
            'user_id' => $request->teacher_id,
            'subject_id' => $request->subject_id,
        ]);

        return back()->with('success', 'Teacher assigned to subject successfully!');
    }

    /**
     * Remove teacher from subject
     */
    public function removeTeacherFromSubject(Request $request)
    {
        $assignment = SubjectTeacher::where('user_id', $request->teacher_id)
                                   ->where('subject_id', $request->subject_id)
                                   ->first();

        if ($assignment) {
            $assignment->delete();
            return back()->with('success', 'Teacher removed from subject successfully!');
        }

        return back()->withErrors(['error' => 'Assignment not found.']);
    }

    /**
     * Assign class teacher
     */
    public function assignClassTeacher(Request $request)
    {
        $request->validate([
            'teacher_id' => 'required|exists:users,id',
            'class_id' => 'required|exists:classes,id',
            'stream_id' => 'required|exists:streams,id',
            'academic_year' => 'required|integer|min:2020|max:2030',
        ]);

        // Check if teacher exists and is actually a teacher
        $teacher = User::where('id', $request->teacher_id)->where('role', 'teacher')->first();
        if (!$teacher) {
            return back()->withErrors(['teacher_id' => 'Selected user is not a teacher.']);
        }

        // Verify that the stream belongs to the selected class
        $stream = Stream::where('id', $request->stream_id)
                       ->where('class_id', $request->class_id)
                       ->first();

        if (!$stream) {
            return back()->withErrors(['stream_id' => 'The selected stream does not belong to the selected class.']);
        }

        // Check if class teacher assignment already exists for this class/stream/year
        $exists = ClassTeacher::where('class_id', $request->class_id)
                             ->where('stream_id', $request->stream_id)
                             ->where('academic_year', $request->academic_year)
                             ->exists();

        if ($exists) {
            return back()->withErrors(['class_id' => 'This class-stream already has a class teacher for this academic year.']);
        }

        ClassTeacher::create([
            'user_id' => $request->teacher_id,
            'class_id' => $request->class_id,
            'stream_id' => $request->stream_id,
            'academic_year' => $request->academic_year,
        ]);

        return back()->with('success', 'Class teacher assigned successfully!');
    }

    /**
     * Remove class teacher
     */
    public function removeClassTeacher(Request $request)
    {
        $assignment = ClassTeacher::where('user_id', $request->teacher_id)
                                 ->where('class_id', $request->class_id)
                                 ->where('stream_id', $request->stream_id)
                                 ->where('academic_year', $request->academic_year)
                                 ->first();

        if ($assignment) {
            $assignment->delete();
            return back()->with('success', 'Class teacher removed successfully!');
        }

        return back()->withErrors(['error' => 'Assignment not found.']);
    }
}
