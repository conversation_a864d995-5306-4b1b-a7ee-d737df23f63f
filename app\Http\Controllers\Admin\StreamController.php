<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Stream;
use App\Models\SchoolClass;
use Illuminate\Http\Request;

class StreamController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $streams = Stream::with(['schoolClass'])->withCount(['enrollments'])->get();
        return view('admin.streams.index', compact('streams'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $classes = SchoolClass::all();
        return view('admin.streams.create', compact('classes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'class_id' => 'required|exists:classes,id',
            'name' => 'required|string|max:10',
        ]);

        // Check if stream already exists for this class
        $exists = Stream::where('class_id', $request->class_id)
                       ->where('name', $request->name)
                       ->exists();

        if ($exists) {
            return back()->withErrors(['name' => 'This stream already exists for the selected class.']);
        }

        Stream::create([
            'class_id' => $request->class_id,
            'name' => $request->name,
        ]);

        return redirect()->route('admin.streams.index')
            ->with('success', 'Stream created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Stream $stream)
    {
        $stream->load(['schoolClass', 'enrollments.user']);
        return view('admin.streams.show', compact('stream'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Stream $stream)
    {
        $classes = SchoolClass::all();
        return view('admin.streams.edit', compact('stream', 'classes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Stream $stream)
    {
        $request->validate([
            'class_id' => 'required|exists:classes,id',
            'name' => 'required|string|max:10',
        ]);

        // Check if stream already exists for this class (excluding current stream)
        $exists = Stream::where('class_id', $request->class_id)
                       ->where('name', $request->name)
                       ->where('id', '!=', $stream->id)
                       ->exists();

        if ($exists) {
            return back()->withErrors(['name' => 'This stream already exists for the selected class.']);
        }

        $stream->update([
            'class_id' => $request->class_id,
            'name' => $request->name,
        ]);

        return redirect()->route('admin.streams.index')
            ->with('success', 'Stream updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Stream $stream)
    {
        $stream->delete();
        return redirect()->route('admin.streams.index')
            ->with('success', 'Stream deleted successfully!');
    }
}
