<!-- Subjects Management Tab -->
<div class="space-y-6">
    <!-- Add Subject Form -->
    <div class="bg-gray-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Add New Subject</h3>
        <form action="{{ route('admin.subjects.store') }}" method="POST" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            @csrf
            <div>
                <label class="block text-sm font-medium text-gray-700">Subject Name</label>
                <input type="text" name="name" placeholder="e.g., Mathematics, Science, English" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <button type="submit" class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded mt-6">
                    Add Subject
                </button>
            </div>
        </form>
    </div>

    <!-- Subjects List -->
    <div>
        <h3 class="text-lg font-semibold mb-4">All Subjects</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full table-auto border">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-left">Subject Name</th>
                        <th class="px-4 py-2 text-left">Assigned Teachers</th>
                        <th class="px-4 py-2 text-left">Total Grades</th>
                        <th class="px-4 py-2 text-left">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach(\App\Models\Subject::withCount(['subjectTeachers', 'grades'])->get() as $subject)
                        <tr class="border-t">
                            <td class="px-4 py-2 font-medium">{{ $subject->name }}</td>
                            <td class="px-4 py-2">{{ $subject->subject_teachers_count }} teachers</td>
                            <td class="px-4 py-2">{{ $subject->grades_count }} grades</td>
                            <td class="px-4 py-2">
                                <a href="{{ route('admin.subjects.edit', $subject) }}" class="text-blue-600 hover:text-blue-900 mr-2">Edit</a>
                                <form action="{{ route('admin.subjects.destroy', $subject) }}" method="POST" class="inline">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-900"
                                            onclick="return confirm('Are you sure?')">Delete</button>
                                </form>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-yellow-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <button onclick="createQuickSubject('Mathematics')" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                + Mathematics
            </button>
            <button onclick="createQuickSubject('Science')" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm">
                + Science
            </button>
            <button onclick="createQuickSubject('English')" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded text-sm">
                + English
            </button>
            <button onclick="createAllSubjects()" class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded text-sm">
                + Create All Basic
            </button>
        </div>
    </div>
</div>

<script>
function createQuickSubject(subjectName) {
    document.querySelector('input[name="name"]').value = subjectName;
}

function createAllSubjects() {
    const subjects = ['Mathematics', 'Science', 'English', 'History', 'Geography', 'Art', 'Physical Education', 'Computer Science'];
    alert('Will create: ' + subjects.join(', ') + '\n\nClick each subject button and submit to create them one by one.');
}
</script>
