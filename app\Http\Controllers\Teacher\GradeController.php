<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\Grade;
use App\Models\Enrollment;
use App\Models\SubjectTeacher;
use Illuminate\Http\Request;

class GradeController extends Controller
{
    public function index(Request $request)
    {
        $teacher = auth()->user();

        // Get teacher's assigned subjects
        $assignedSubjects = SubjectTeacher::with('subject')
            ->where('user_id', $teacher->id)
            ->get();

        $selectedSubject = $request->get('subject');
        $selectedTerm = $request->get('term', 1);

        $grades = collect();
        $enrollments = collect();

        if ($selectedSubject) {
            // Get all enrollments for current year
            $enrollments = Enrollment::with(['user', 'schoolClass', 'stream'])
                ->where('academic_year', date('Y'))
                ->get();

            // Get existing grades for this subject and term
            $grades = Grade::with(['enrollment.user'])
                ->where('subject_id', $selectedSubject)
                ->where('teacher_id', $teacher->id)
                ->where('term', $selectedTerm)
                ->get()
                ->keyBy('enrollment_id');
        }

        return view('teacher.grades.index', compact(
            'assignedSubjects', 'selectedSubject', 'selectedTerm', 'enrollments', 'grades'
        ));
    }

    public function store(Request $request)
    {
        $request->validate([
            'enrollment_id' => 'required|exists:enrollments,id',
            'subject_id' => 'required|exists:subjects,id',
            'term' => 'required|integer|min:1|max:3',
            'assignment' => 'required|numeric|min:0|max:100',
            'midterm' => 'required|numeric|min:0|max:100',
            'final' => 'required|numeric|min:0|max:100',
        ]);

        Grade::updateOrCreate(
            [
                'enrollment_id' => $request->enrollment_id,
                'subject_id' => $request->subject_id,
                'term' => $request->term,
            ],
            [
                'teacher_id' => auth()->id(),
                'assignment' => $request->assignment,
                'midterm' => $request->midterm,
                'final' => $request->final,
            ]
        );

        return redirect()->back()->with('success', 'Grade saved successfully!');
    }
}
