<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Enrollment;
use App\Models\User;
use App\Models\SchoolClass;
use App\Models\Stream;
use Illuminate\Http\Request;

class EnrollmentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $enrollments = Enrollment::with(['user', 'schoolClass', 'stream'])
                                ->orderBy('academic_year', 'desc')
                                ->orderBy('class_id')
                                ->orderBy('stream_id')
                                ->get();
        return view('admin.enrollments.index', compact('enrollments'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $students = User::students()->get();
        $classes = SchoolClass::with('streams')->get();
        return view('admin.enrollments.create', compact('students', 'classes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'class_id' => 'required|exists:classes,id',
            'stream_id' => 'required|exists:streams,id',
            'academic_year' => 'required|integer|min:2020|max:2030',
        ]);

        // Check if student is already enrolled in this class/stream for this year
        $exists = Enrollment::where('user_id', $request->user_id)
                           ->where('academic_year', $request->academic_year)
                           ->exists();

        if ($exists) {
            return back()->withErrors(['user_id' => 'This student is already enrolled for the selected academic year.']);
        }

        // Verify that the stream belongs to the selected class
        $stream = Stream::where('id', $request->stream_id)
                       ->where('class_id', $request->class_id)
                       ->first();

        if (!$stream) {
            return back()->withErrors(['stream_id' => 'The selected stream does not belong to the selected class.']);
        }

        Enrollment::create([
            'user_id' => $request->user_id,
            'class_id' => $request->class_id,
            'stream_id' => $request->stream_id,
            'academic_year' => $request->academic_year,
        ]);

        return redirect()->route('admin.enrollments.index')
            ->with('success', 'Student enrolled successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Enrollment $enrollment)
    {
        $enrollment->load(['user', 'schoolClass', 'stream', 'grades.subject']);
        return view('admin.enrollments.show', compact('enrollment'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Enrollment $enrollment)
    {
        $students = User::students()->get();
        $classes = SchoolClass::with('streams')->get();
        return view('admin.enrollments.edit', compact('enrollment', 'students', 'classes'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Enrollment $enrollment)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'class_id' => 'required|exists:classes,id',
            'stream_id' => 'required|exists:streams,id',
            'academic_year' => 'required|integer|min:2020|max:2030',
        ]);

        // Check if student is already enrolled in this class/stream for this year (excluding current enrollment)
        $exists = Enrollment::where('user_id', $request->user_id)
                           ->where('academic_year', $request->academic_year)
                           ->where('id', '!=', $enrollment->id)
                           ->exists();

        if ($exists) {
            return back()->withErrors(['user_id' => 'This student is already enrolled for the selected academic year.']);
        }

        // Verify that the stream belongs to the selected class
        $stream = Stream::where('id', $request->stream_id)
                       ->where('class_id', $request->class_id)
                       ->first();

        if (!$stream) {
            return back()->withErrors(['stream_id' => 'The selected stream does not belong to the selected class.']);
        }

        $enrollment->update([
            'user_id' => $request->user_id,
            'class_id' => $request->class_id,
            'stream_id' => $request->stream_id,
            'academic_year' => $request->academic_year,
        ]);

        return redirect()->route('admin.enrollments.index')
            ->with('success', 'Enrollment updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Enrollment $enrollment)
    {
        $enrollment->delete();
        return redirect()->route('admin.enrollments.index')
            ->with('success', 'Enrollment deleted successfully!');
    }

    /**
     * Get streams for a specific class (AJAX endpoint)
     */
    public function getStreams(Request $request)
    {
        $classId = $request->get('class_id');
        $streams = Stream::where('class_id', $classId)->get();
        
        return response()->json($streams);
    }
}
