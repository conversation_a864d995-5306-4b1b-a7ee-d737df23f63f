<!-- Subjects Management Tab -->
<div class="space-y-6">
    <!-- Add Subject Form -->
    <div class="bg-gray-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Add New Subject</h3>
        <form action="<?php echo e(route('admin.subjects.store')); ?>" method="POST" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <?php echo csrf_field(); ?>
            <div>
                <label class="block text-sm font-medium text-gray-700">Subject Name</label>
                <input type="text" name="name" placeholder="e.g., Mathematics, Science, English" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
            </div>
            <div>
                <button type="submit" class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded mt-6">
                    Add Subject
                </button>
            </div>
        </form>
    </div>

    <!-- Subjects List -->
    <div>
        <h3 class="text-lg font-semibold mb-4">All Subjects</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full table-auto border">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-left">Subject Name</th>
                        <th class="px-4 py-2 text-left">Assigned Teachers</th>
                        <th class="px-4 py-2 text-left">Total Grades</th>
                        <th class="px-4 py-2 text-left">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = \App\Models\Subject::withCount(['subjectTeachers', 'grades'])->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="border-t">
                            <td class="px-4 py-2 font-medium"><?php echo e($subject->name); ?></td>
                            <td class="px-4 py-2"><?php echo e($subject->subject_teachers_count); ?> teachers</td>
                            <td class="px-4 py-2"><?php echo e($subject->grades_count); ?> grades</td>
                            <td class="px-4 py-2">
                                <a href="<?php echo e(route('admin.subjects.edit', $subject)); ?>" class="text-blue-600 hover:text-blue-900 mr-2">Edit</a>
                                <form action="<?php echo e(route('admin.subjects.destroy', $subject)); ?>" method="POST" class="inline">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="submit" class="text-red-600 hover:text-red-900" 
                                            onclick="return confirm('Are you sure?')">Delete</button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php /**PATH C:\xampp\htdocs\school_results\resources\views/admin/tabs/subjects.blade.php ENDPATH**/ ?>