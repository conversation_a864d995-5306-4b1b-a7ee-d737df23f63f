<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\SchoolClass;
use App\Models\Subject;
use App\Models\Enrollment;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $stats = [
            'total_students' => User::students()->count(),
            'total_teachers' => User::teachers()->count(),
            'total_classes' => SchoolClass::count(),
            'total_subjects' => Subject::count(),
            'current_enrollments' => Enrollment::forAcademicYear(date('Y'))->count(),
        ];

        $recent_enrollments = Enrollment::with(['user', 'schoolClass', 'stream'])
            ->forAcademicYear(date('Y'))
            ->latest()
            ->take(10)
            ->get();

        return view('admin.dashboard', compact('stats', 'recent_enrollments'));
    }
}
