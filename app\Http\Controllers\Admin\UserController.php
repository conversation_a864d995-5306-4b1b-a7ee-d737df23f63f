<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    public function index()
    {
        $users = User::orderBy('role')->orderBy('name')->get();
        return view('admin.users.index', compact('users'));
    }

    public function create()
    {
        return view('admin.users.create');
    }

    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'role' => 'required|in:admin,teacher,student',
            'password' => 'required|min:8',
        ]);

        User::create([
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
            'password' => Hash::make($request->password),
        ]);

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully!');
    }

    public function edit(User $user)
    {
        return view('admin.users.edit', compact('user'));
    }

    public function update(Request $request, User $user)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'role' => 'required|in:admin,teacher,student',
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'role' => $request->role,
        ]);

        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully!');
    }

    public function destroy(User $user)
    {
        $user->delete();
        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully!');
    }

    /**
     * Store user with automatic assignment based on role
     */
    public function storeWithAssignment(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8',
            'role' => 'required|in:admin,teacher,student',
            // Student fields
            'class_id' => 'required_if:role,student|exists:classes,id',
            'stream_id' => 'required_if:role,student|exists:streams,id',
            'academic_year' => 'required_if:role,student|integer|min:2020|max:2030',
            // Teacher fields
            'subject_ids' => 'array',
            'subject_ids.*' => 'exists:subjects,id',
        ]);

        // Create the user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'role' => $request->role,
        ]);

        $message = 'User created successfully!';

        // Handle role-specific assignments
        if ($request->role === 'student' && $request->class_id && $request->stream_id) {
            // Check if student is already enrolled
            $existingEnrollment = \App\Models\Enrollment::where('user_id', $user->id)
                                                        ->where('academic_year', $request->academic_year)
                                                        ->exists();

            if (!$existingEnrollment) {
                \App\Models\Enrollment::create([
                    'user_id' => $user->id,
                    'class_id' => $request->class_id,
                    'stream_id' => $request->stream_id,
                    'academic_year' => $request->academic_year,
                ]);
                $message .= ' Student enrolled in class successfully!';
            }
        }

        if ($request->role === 'teacher' && $request->subject_ids) {
            foreach ($request->subject_ids as $subjectId) {
                // Check if assignment already exists
                $existingAssignment = \App\Models\SubjectTeacher::where('user_id', $user->id)
                                                                ->where('subject_id', $subjectId)
                                                                ->exists();

                if (!$existingAssignment) {
                    \App\Models\SubjectTeacher::create([
                        'user_id' => $user->id,
                        'subject_id' => $subjectId,
                    ]);
                }
            }
            $message .= ' Teacher assigned to subjects successfully!';
        }

        return redirect()->route('admin.dashboard')
            ->with('success', $message);
    }
}
