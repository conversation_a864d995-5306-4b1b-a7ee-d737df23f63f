<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Teacher\DashboardController as TeacherDashboardController;
use App\Http\Controllers\Student\DashboardController as StudentDashboardController;
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', function () {
    return redirect()->route('login');
});

// Authenticated routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Main dashboard - redirects to role-specific dashboard
    Route::get('/dashboard', DashboardController::class)->name('dashboard');

    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Admin routes
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

    // User management
    Route::resource('users', App\Http\Controllers\Admin\UserController::class);

    // Class management
    Route::resource('classes', App\Http\Controllers\Admin\ClassController::class);

    // Stream management
    Route::resource('streams', App\Http\Controllers\Admin\StreamController::class);

    // Subject management
    Route::resource('subjects', App\Http\Controllers\Admin\SubjectController::class);

    // Enrollment management
    Route::resource('enrollments', App\Http\Controllers\Admin\EnrollmentController::class);
    Route::get('/get-streams', [App\Http\Controllers\Admin\EnrollmentController::class, 'getStreams'])->name('get-streams');
});

// Teacher routes
Route::middleware(['auth', 'teacher'])->prefix('teacher')->name('teacher.')->group(function () {
    Route::get('/dashboard', [TeacherDashboardController::class, 'index'])->name('dashboard');

    // Grade management
    Route::get('/grades', [App\Http\Controllers\Teacher\GradeController::class, 'index'])->name('grades.index');
    Route::post('/grades', [App\Http\Controllers\Teacher\GradeController::class, 'store'])->name('grades.store');
});

// Class Teacher routes
Route::middleware(['auth', 'class_teacher'])->prefix('class-teacher')->name('class_teacher.')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\ClassTeacher\DashboardController::class, 'index'])->name('dashboard');
});

// Student routes
Route::middleware(['auth', 'student'])->prefix('student')->name('student.')->group(function () {
    Route::get('/dashboard', [StudentDashboardController::class, 'index'])->name('dashboard');
});

require __DIR__.'/auth.php';
