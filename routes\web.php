<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Teacher\DashboardController as TeacherDashboardController;
use App\Http\Controllers\Student\DashboardController as StudentDashboardController;
use Illuminate\Support\Facades\Route;

// Public routes
Route::get('/', function () {
    return redirect()->route('login');
});

// Authenticated routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Main dashboard - redirects to role-specific dashboard
    Route::get('/dashboard', DashboardController::class)->name('dashboard');

    // Profile routes - Admin only
    Route::middleware('admin')->group(function () {
        Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
        Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
        Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    });
});

// Simple Admin routes - Everything in one controller
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

    // All CRUD operations in one simple controller
    Route::post('/create-user', [AdminDashboardController::class, 'createUser'])->name('create-user');
    Route::post('/create-class', [AdminDashboardController::class, 'createClass'])->name('create-class');
    Route::post('/create-stream', [AdminDashboardController::class, 'createStream'])->name('create-stream');
    Route::post('/create-subject', [AdminDashboardController::class, 'createSubject'])->name('create-subject');
    Route::post('/enroll-student', [AdminDashboardController::class, 'enrollStudent'])->name('enroll-student');
    Route::post('/assign-teacher', [AdminDashboardController::class, 'assignTeacher'])->name('assign-teacher');
    Route::post('/assign-class-teacher', [AdminDashboardController::class, 'assignClassTeacher'])->name('assign-class-teacher');

    // Simple delete routes
    Route::delete('/delete/{type}/{id}', [AdminDashboardController::class, 'delete'])->name('delete');
});

// Teacher routes
Route::middleware(['auth', 'teacher'])->prefix('teacher')->name('teacher.')->group(function () {
    Route::get('/dashboard', [TeacherDashboardController::class, 'index'])->name('dashboard');

    // Grade management
    Route::get('/grades', [App\Http\Controllers\Teacher\GradeController::class, 'index'])->name('grades.index');
    Route::post('/grades', [App\Http\Controllers\Teacher\GradeController::class, 'store'])->name('grades.store');
});

// Class Teacher routes
Route::middleware(['auth', 'class_teacher'])->prefix('class-teacher')->name('class_teacher.')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\ClassTeacher\DashboardController::class, 'index'])->name('dashboard');
});

// Student routes
Route::middleware(['auth', 'student'])->prefix('student')->name('student.')->group(function () {
    Route::get('/dashboard', [StudentDashboardController::class, 'index'])->name('dashboard');
});

require __DIR__.'/auth.php';
