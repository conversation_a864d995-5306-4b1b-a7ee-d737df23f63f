<!-- Users Management Tab -->
<div class="space-y-6">
    <!-- Add User Form -->
    <div class="bg-gray-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Add New User</h3>
        <form action="{{ route('admin.users.store-with-assignment') }}" method="POST" id="userForm">
            @csrf

            <!-- Basic User Info -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Name</label>
                    <input type="text" name="name" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Email</label>
                    <input type="email" name="email" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Role</label>
                    <select name="role" id="userRole" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" onchange="toggleAssignmentFields()">
                        <option value="">Select Role</option>
                        <option value="admin">Admin</option>
                        <option value="teacher">Teacher</option>
                        <option value="student">Student</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Password</label>
                    <input type="password" name="password" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                </div>
            </div>

            <!-- Student Assignment Fields (Hidden by default) -->
            <div id="studentFields" class="hidden bg-blue-50 p-4 rounded mb-4">
                <h4 class="font-medium mb-3 text-blue-800">Student Class Assignment</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Select Class</label>
                        <select name="class_id" id="studentClassId" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                            <option value="">Choose Class</option>
                            @foreach(\App\Models\SchoolClass::all() as $class)
                                <option value="{{ $class->id }}">{{ $class->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Select Stream</label>
                        <select name="stream_id" id="studentStreamId" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                            <option value="">First select class</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Academic Year</label>
                        <input type="number" name="academic_year" value="{{ date('Y') }}" min="2020" max="2030" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm">
                    </div>
                </div>
            </div>

            <!-- Teacher Assignment Fields (Hidden by default) -->
            <div id="teacherFields" class="hidden bg-green-50 p-4 rounded mb-4">
                <h4 class="font-medium mb-3 text-green-800">Teacher Subject Assignment</h4>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Select Subjects (Hold Ctrl to select multiple)</label>
                    <select name="subject_ids[]" multiple class="mt-1 block w-full rounded-md border-gray-300 shadow-sm" size="5">
                        @foreach(\App\Models\Subject::all() as $subject)
                            <option value="{{ $subject->id }}">{{ $subject->name }}</option>
                        @endforeach
                    </select>
                    <p class="text-sm text-gray-500 mt-1">Hold Ctrl (or Cmd on Mac) and click to select multiple subjects</p>
                </div>
            </div>

            <div>
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Create User & Assign
                </button>
            </div>
        </form>
    </div>

    <!-- Users List -->
    <div>
        <h3 class="text-lg font-semibold mb-4">All Users</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full table-auto border">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-left">Name</th>
                        <th class="px-4 py-2 text-left">Email</th>
                        <th class="px-4 py-2 text-left">Role</th>
                        <th class="px-4 py-2 text-left">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach(\App\Models\User::all() as $user)
                        <tr class="border-t">
                            <td class="px-4 py-2">{{ $user->name }}</td>
                            <td class="px-4 py-2">{{ $user->email }}</td>
                            <td class="px-4 py-2">
                                <span class="px-2 py-1 text-xs rounded
                                    @if($user->role === 'admin') bg-red-100 text-red-800
                                    @elseif($user->role === 'teacher') bg-blue-100 text-blue-800
                                    @else bg-green-100 text-green-800
                                    @endif">
                                    {{ ucfirst($user->role) }}
                                </span>
                            </td>
                            <td class="px-4 py-2">
                                <a href="{{ route('admin.users.edit', $user) }}" class="text-blue-600 hover:text-blue-900 mr-2">Edit</a>
                                @if($user->id !== auth()->id())
                                    <form action="{{ route('admin.users.destroy', $user) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="text-red-600 hover:text-red-900"
                                                onclick="return confirm('Are you sure?')">Delete</button>
                                    </form>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-yellow-50 p-4 rounded">
        <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <button onclick="createQuickUser('admin')" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded text-sm">
                + Quick Admin
            </button>
            <button onclick="createQuickUser('teacher')" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                + Quick Teacher
            </button>
            <button onclick="createQuickUser('student')" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm">
                + Quick Student
            </button>
            <button onclick="resetForm()" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded text-sm">
                Reset Form
            </button>
        </div>
    </div>
</div>

<script>
function toggleAssignmentFields() {
    const role = document.getElementById('userRole').value;
    const studentFields = document.getElementById('studentFields');
    const teacherFields = document.getElementById('teacherFields');

    // Hide all fields first
    studentFields.classList.add('hidden');
    teacherFields.classList.add('hidden');

    // Show relevant fields based on role
    if (role === 'student') {
        studentFields.classList.remove('hidden');
    } else if (role === 'teacher') {
        teacherFields.classList.remove('hidden');
    }
}

// Handle class selection for students
document.getElementById('studentClassId').addEventListener('change', function() {
    const classId = this.value;
    const streamSelect = document.getElementById('studentStreamId');

    streamSelect.innerHTML = '<option value="">Loading...</option>';

    if (classId) {
        const classData = @json(\App\Models\SchoolClass::with('streams')->get());
        const selectedClass = classData.find(c => c.id == classId);

        streamSelect.innerHTML = '<option value="">Choose Stream</option>';

        if (selectedClass && selectedClass.streams) {
            selectedClass.streams.forEach(stream => {
                const option = document.createElement('option');
                option.value = stream.id;
                option.textContent = stream.name;
                streamSelect.appendChild(option);
            });
        }
    } else {
        streamSelect.innerHTML = '<option value="">First select class</option>';
    }
});

// Quick user creation functions
function createQuickUser(role) {
    document.getElementById('userRole').value = role;
    toggleAssignmentFields();

    // Pre-fill some demo data
    const nameField = document.querySelector('input[name="name"]');
    const emailField = document.querySelector('input[name="email"]');
    const passwordField = document.querySelector('input[name="password"]');

    const timestamp = Date.now();
    nameField.value = `${role.charAt(0).toUpperCase() + role.slice(1)} ${timestamp.toString().slice(-4)}`;
    emailField.value = `${role}${timestamp.toString().slice(-4)}@school.com`;
    passwordField.value = 'password123';
}

function resetForm() {
    document.getElementById('userForm').reset();
    toggleAssignmentFields();
}
</script>
